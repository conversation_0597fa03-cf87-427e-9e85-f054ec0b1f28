# Supabase DB - Setup & Deployment Guide

## 1. Local Setup

### Step 1: Create environment file
```bash
cd docker
cp .env.example .env
```

### Step 2: Pull Docker images
```bash
docker compose pull
```

### Step 3: Start Supabase services
```bash
docker compose up -d
```

### Step 4: Connect to Database (local)
Example configuration for Django:
```python
DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql_psycopg2",
        "NAME": "postgres",
        "USER": "postgres.admin",      # POOLER_TENANT_ID from .env
        "PASSWORD": "leadplus123",     # POSTGRES_PASSWORD from .env
        "HOST": "localhost",
        "PORT": 6543,
    }
}
```

---

## 2. Deploy to Server (GCP)

### Step 1: Create VM on Google Cloud
```bash
gcloud config set project YOUR-GCP-PROJECT-ID
gcloud compute instances create VM-NAME \
  --project=YOUR-GCP-PROJECT-ID \
  --zone=asia-northeast1-b \
  --machine-type=e2-medium \
  --provisioning-model=SPOT \
  --instance-termination-action=STOP \
  --image-family=debian-11 \
  --image-project=debian-cloud \
  --boot-disk-size=40GB \
  --boot-disk-type=pd-balanced \
  --boot-disk-device-name=supabase-test \
  --tags=http-server,https-server
```

### Step 2: Deploy code to VM
- Edit variables in `deploy.sh`:
  ```bash
  GCP_PROJECT=YOUR-GCP-PROJECT-ID
  VM_NAME=VM-NAME
  ```
- Run:
  ```bash
  bash deploy.sh
  ```

### Step 3: Connect to Database (on server)
Example configuration for Django:
```python
DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql_psycopg2",
        "NAME": "postgres",
        "USER": "postgres.admin",      # POOLER_TENANT_ID from .env.dev
        "PASSWORD": "leadplus123",     # POSTGRES_PASSWORD from .env.dev
        "HOST": "**************",      # Public IP of VM on GCP
        "PORT": 5432,
    }
}
```

---

## 3. Notes

- **Supabase Studio:** Access at `http://<VM_PUBLIC_IP>:8000` (if firewall is open).
- **Connect to DB from outside:** Make sure port 5432 is open in the firewall and the VM has the correct network tag.


---

