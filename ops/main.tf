module "mig" {
  source     = "./modules/mig"
  project_id = var.project_id
  region     = var.region
  zone       = var.zone
  env        = var.env
}

module "lb" {
  source        = "./modules/lb"
  project_id    = var.project_id
  region        = var.region
  env           = var.env
  instance_group = module.mig.instance_group
  domain_name    = "${var.env}.supabase.leadplus.dev"
}

module "dns" {
  source              = "./modules/dns"
  project_id_domain   = var.project_id_domain
  ip_address          = module.lb.lb_ip
  dns_zone            = var.dns_zone
  env                 = var.env
}