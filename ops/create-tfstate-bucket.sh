#!/usr/bin/env bash
set -euo pipefail

# === Read config from gcloud ===
PROJECT_ID=$(gcloud config get-value project 2>/dev/null)
LOCATION=$(gcloud config get-value compute/region 2>/dev/null)

# === Validate PROJECT_ID ===
if [[ -z "${PROJECT_ID}" || "${PROJECT_ID}" == "(unset)" ]]; then
  echo "No project set in gcloud config."
  echo "Run: gcloud config set project PROJECT_ID"
  exit 1
fi

# === Validate LOCATION ===
if [[ -z "${LOCATION}" || "${LOCATION}" == "(unset)" ]]; then
  echo "No region set in gcloud config."
  echo "Run: gcloud config set compute/region REGION"
  exit 1
fi

# === Config variables ===
BUCKET_NAME="leadplus-supabase-tfstate-${PROJECT_ID}"   # must be globally unique
STORAGE_CLASS="STANDARD"
RETENTION_DAYS=30

echo ">>> Using project:  ${PROJECT_ID}"
echo ">>> Using location: ${LOCATION}"
echo ">>> Bucket name:    ${BUCKET_NAME}"

# === Create bucket ===
gcloud storage buckets create gs://${BUCKET_NAME} \
  --project="${PROJECT_ID}" \
  --location="${LOCATION}" \
  --uniform-bucket-level-access \
  --pap \
  --default-storage-class="${STORAGE_CLASS}"

# === Enable object versioning ===
gsutil versioning set on gs://${BUCKET_NAME}

# === Set retention policy ===
gcloud storage buckets update gs://${BUCKET_NAME} \
  --retention-period="${RETENTION_DAYS}d"

# === Show final config ===
gcloud storage buckets describe gs://${BUCKET_NAME} --project="${PROJECT_ID}" --format="yaml"