variable "project_id" {
  type        = string
  description = "GCP Supabase Project ID"
}

variable "project_id_domain" {
  type        = string
  description = "GCP Domain Project ID"
}

variable "region" {
  type        = string
  description = "Region"
  default     = "us-northeast1"
}

variable "zone" {
  type        = string
  description = "Zone"
  default     = "us-northeast1-a"
}

variable "env" {
  type        = string
  description = "Environment name (dev, test, prod)"
}

variable "dns_zone" {
  type        = string
  description = "Pre-created Cloud DNS managed zone"
  default     = "leadplus-dev"
}