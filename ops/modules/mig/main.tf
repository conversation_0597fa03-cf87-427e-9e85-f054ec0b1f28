# Instance Template (VM specification)
resource "google_compute_instance_template" "supabase_template" {
  name_prefix  = "supabase-template-${var.env}-"
  machine_type = "e2-medium"

  disk {
    source_image = "debian-cloud/debian-11"
    auto_delete  = true
    boot         = true
    disk_size_gb = 20
  }

  disk {
    auto_delete  = false
    boot         = false
    source       = google_compute_region_disk.supabase_data.id
    device_name  = google_compute_region_disk.supabase_data.name
  }

  network_interface {
    network       = "default"
    # no need for external IP
    # access_config {}
  }

  tags = ["supabase", var.env]

  metadata = {
    startup-script = <<-EOT
      #!/bin/bash
      apt-get update -y
      apt-get install -y apache2

      echo "<h1>Hello from Supabase VM (${var.env})</h1>" > /var/www/html/index.html

      systemctl enable apache2
      systemctl restart apache2
    EOT
  }

  lifecycle {
    create_before_destroy = true
  }
}

# Stateful Data Disk (outside of template)
resource "google_compute_region_disk" "supabase_data" {
  name  = "supabase-data-${var.env}"
  type  = "pd-standard"
  size  = 200 # regional disk can't be smaller than 200GB

  region = var.region
  replica_zones = [
    "${var.region}-a",
    "${var.region}-b"
  ]
}

# Managed Instance Group (MIG) with stateful disk
resource "google_compute_instance_group_manager" "supabase_mig" {
  name               = "supabase-mig-${var.env}"
  base_instance_name = "supabase-${var.env}"
  zone               = var.zone

  version {
    instance_template = google_compute_instance_template.supabase_template.id
  }

  target_size = 1 # count of VMs in the MIG

  # Attach the stateful disk to MIG
  stateful_disk {
    device_name = google_compute_region_disk.supabase_data.name
    delete_rule = "NEVER" # keep data even if VM is deleted
  }
}

# Per-instance config to attach existing stateful disk to the MIG VM
resource "google_compute_per_instance_config" "supabase_config" {
  name                     = "supabase-${var.env}"
  instance_group_manager   = google_compute_instance_group_manager.supabase_mig.name
  zone                     = var.zone

  preserved_state {
    disk {
      device_name = "supabase-data-${var.env}"
      source      = google_compute_region_disk.supabase_data.id
      mode        = "READ_WRITE"
    }
  }
}

resource "google_compute_resource_policy" "daily_snapshot" {
  name   = "supabase-schedule-${var.env}"
  region = var.region

  snapshot_schedule_policy {
    schedule {
      daily_schedule {
        days_in_cycle = 1
        start_time    = "03:00"
      }
    }

    retention_policy {
      max_retention_days    = 7
      on_source_disk_delete = "KEEP_AUTO_SNAPSHOTS"
    }

    snapshot_properties {
      storage_locations = [var.region]
      labels = {
        env = var.env
      }
    }
  }
}

resource "google_compute_region_disk_resource_policy_attachment" "attach_schedule" {
  name   = google_compute_resource_policy.daily_snapshot.name
  disk   = google_compute_region_disk.supabase_data.name
  region = var.region
}