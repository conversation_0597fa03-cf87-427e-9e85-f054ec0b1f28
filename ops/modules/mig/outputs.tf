output "mig_name" {
  description = "Name of the Managed Instance Group"
  value       = google_compute_instance_group_manager.supabase_mig.name
}

output "instance_template" {
  description = "Name of the instance template used by MIG"
  value       = google_compute_instance_template.supabase_template.name
}

output "instance_group" {
  description = "Self link of the Managed Instance Group for Supabase"
  value       = google_compute_instance_group_manager.supabase_mig.instance_group
}

output "vm_name" {
  description = "Name of the first VM in the MIG"
  value       = regex("([^/]+)$", tolist(data.google_compute_instance_group.supabase_group.instances)[0].instance)
}

output "disk_name" {
  description = "Name of the stateful data disk"
  value       = google_compute_region_disk.supabase_data.name
}

output "snapshot_name" {
  description = "Latest snapshot resource name"
  value       = google_compute_snapshot.supabase_data_snapshot.name
}