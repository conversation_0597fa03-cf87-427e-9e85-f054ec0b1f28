resource "google_compute_global_address" "lb_ip_supabase" {
  name = "lb-ip-supabase-${var.env}"
}

resource "google_compute_backend_service" "backend_supabase" {
  name                  = "backend-supabase-${var.env}"
  protocol              = "HTTP"
  port_name             = "http"
  timeout_sec           = 30
  connection_draining_timeout_sec = 10
  backend {
    group = var.instance_group
  }
  health_checks = [google_compute_health_check.hc_supabase.id]
}

resource "google_compute_health_check" "hc_supabase" {
  name = "hc-supabase-${var.env}"
  http_health_check {
    port = 80
  }
}

resource "google_compute_url_map" "urlmap_supabase" {
  name            = "urlmap-supabase-${var.env}"
  default_service = google_compute_backend_service.backend_supabase.id
}

resource "google_compute_target_https_proxy" "hppts_proxy_supabase" {
  name             = "https-proxy-supabase-${var.env}"
  url_map          = google_compute_url_map.urlmap_supabase.id
  ssl_certificates = [google_compute_managed_ssl_certificate.cert_supabase.id]
}

resource "google_compute_managed_ssl_certificate" "cert_supabase" {
  name = "cert-supabase-${var.env}"
  managed {
    domains = [var.domain_name]
  }
}

resource "google_compute_global_forwarding_rule" "https-rule-supabase" {
  name                  = "https-rule-supabase-${var.env}"
  target                = google_compute_target_https_proxy.hppts_proxy_supabase.id
  port_range            = "443"
  load_balancing_scheme = "EXTERNAL"
  ip_address            = google_compute_global_address.lb_ip_supabase.id
}

resource "google_compute_firewall" "allow_lb_to_vm_supabase" {
  name    = "allow-lb-to-vm-supabase-${var.env}"
  network = "default"

  allow {
    protocol = "tcp"
    ports    = ["80"]
  }

  # Google LB health checks + data plane
  source_ranges = [
    "**********/16",
    "***********/22"
  ]

  target_tags = ["supabase"]
}