#!/bin/bash

# --- Configuration ---
GCP_PROJECT="nimbus2-data-warehouse"
VM_NAME="supabase-db"
VM_ZONE="asia-northeast1-b"
DEPLOY_DIR="/opt/supabase_db"
SYSTEM_USER="supabase"
SYSTEM_GROUP="supabase"

# Copy file .env.dev to .env
cp docker/.env.dev docker/.env

# --- Script Start ---
set -e

# --- Auto-populate .env values ---
VM_PUBLIC_IP=$(gcloud compute instances describe $VM_NAME --zone $VM_ZONE --project $GCP_PROJECT --format='get(networkInterfaces[0].accessConfigs[0].natIP)')

for VAR in SITE_URL API_EXTERNAL_URL SUPABASE_PUBLIC_URL; do
  if grep -q "^$VAR=" docker/.env; then
    sed -i '' "s|^$VAR=.*|$VAR=http://$VM_PUBLIC_IP:8000|" docker/.env
  else
    echo "$VAR=http://$VM_PUBLIC_IP:8000" >> docker/.env
  fi
done

echo "--- Packaging local code ---"
tar --exclude='docker/volumes/db/data' -czf supabase-deploy.tar.gz docker/ deploy.sh README.md

echo "--- Uploading code to VM using gcloud ---"
gcloud compute scp supabase-deploy.tar.gz $VM_NAME:~ --zone $VM_ZONE --project $GCP_PROJECT

echo "--- SSH into VM and deploy ---"
gcloud compute ssh "$VM_NAME" --zone "$VM_ZONE" --project "$GCP_PROJECT" --command="
    set -e

    # Create supabase user if it doesn't exist
    if ! id -u $SYSTEM_USER &>/dev/null; then
        sudo adduser --system --no-create-home --group $SYSTEM_USER
    fi
    sudo mkdir -p $DEPLOY_DIR
    sudo chown -R $SYSTEM_USER:$SYSTEM_GROUP $DEPLOY_DIR

    echo '--- Stopping and removing old Docker containers if any ---'
    if [ -d \"$DEPLOY_DIR/docker\" ]; then
        cd $DEPLOY_DIR/docker
        sudo -u $SYSTEM_USER docker compose down || true
    fi

    echo '--- Extracting code ---'
    sudo tar xzf ~/supabase-deploy.tar.gz -C $DEPLOY_DIR
    sudo chown -R $SYSTEM_USER:$SYSTEM_GROUP $DEPLOY_DIR
    cd $DEPLOY_DIR/docker

    echo '--- Installing Docker if needed ---'
    if ! command -v docker &> /dev/null; then
        cd /tmp
        curl -fsSL https://get.docker.com -o get-docker.sh
        sudo sh get-docker.sh
        sudo usermod -aG docker $SYSTEM_USER
        rm get-docker.sh
        cd -
    fi

    echo '--- Installing Docker Compose v2 (Plugin) if needed ---'
    if ! docker compose version &> /dev/null; then
        sudo apt-get install -y docker-compose-plugin
    fi

    echo '--- Starting Supabase ---'
    sudo -u $SYSTEM_USER docker compose pull
    sudo -u $SYSTEM_USER docker compose up -d

    echo '--- Cleaning up archive file ---'
    rm ~/supabase-deploy.tar.gz

    echo '--- Deployment complete ---'

    sudo chown root:docker /var/run/docker.sock
    sudo chmod 660 /var/run/docker.sock
"

echo ""
echo "--- Deployment Script Finished Successfully ---"
echo ""
echo "--- Next Steps ---"
echo "1. SSH into the VM to edit the .env file if needed:"
echo "   gcloud compute ssh $VM_NAME --zone $VM_ZONE --project $GCP_PROJECT"
echo "   cd $DEPLOY_DIR/docker && nano .env"
echo "2. Restart services if needed:"
echo "   sudo docker compose down && sudo docker compose up -d"
echo "3. Check container status:"
echo "   sudo docker compose ps"
echo "4. Ensure firewall and DNS are properly configured."

# --- Create firewall rule for Supabase Studio (if not exists) ---
if ! gcloud compute firewall-rules list --filter="name=allow-supabase-studio" --format="value(name)" | grep -q "allow-supabase-studio"; then
  gcloud compute firewall-rules create allow-supabase-studio \
    --allow tcp:8000 \
    --source-ranges=0.0.0.0/0 \
    --target-tags=supabase-db \
    --project=$GCP_PROJECT
else
  echo "Firewall rule 'allow-supabase-studio' exists."
fi
# --- Create firewall rule for PostgreSQL (if not exists) ---
if ! gcloud compute firewall-rules list --filter="name=allow-postgres" --format="value(name)" | grep -q "allow-postgres"; then
  gcloud compute firewall-rules create allow-postgres \
    --allow tcp:5432 \
    --source-ranges=0.0.0.0/0 \
    --target-tags=supabase-db \
    --project=$GCP_PROJECT
else
  echo "Firewall rule 'allow-postgres' exists."
fi

# --- Add network tag to VM ---
if ! gcloud compute instances describe $VM_NAME --zone $VM_ZONE --project $GCP_PROJECT --format="get(tags.items)" | grep -q "supabase-db"; then
  gcloud compute instances add-tags $VM_NAME --tags=supabase-db --zone=$VM_ZONE --project=$GCP_PROJECT
  echo "Added network tag 'supabase-db' to VM."
else
  echo "VM already has network tag 'supabase-db'."
fi

# Remove local archive after deployment
rm -f supabase-deploy.tar.gz

# --- Create firewall rule for Supabase Pooler (if not exists) ---
if ! gcloud compute firewall-rules list --filter="name=allow-supabase-pooler" --format="value(name)" | grep -q "allow-supabase-pooler"; then
  gcloud compute firewall-rules create allow-supabase-pooler \
    --allow tcp:6543 \
    --source-ranges=0.0.0.0/0 \
    --target-tags=supabase-db \
    --project=$GCP_PROJECT
else
  echo "Firewall rule 'allow-supabase-pooler' exists."
fi
